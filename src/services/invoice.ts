import { ApiListResponse, ApiResponse } from "../types/api";
import type {
  InvoiceInfoItem,
  InvoiceSearchParams,
  PostPaidInvoiceItem,
  PostPaidInvoiceSearchParams,
  PrePaidInvoiceItem,
  PrePaidInvoiceSearchParams,
  PrePaidInvoiceApiRequest,
  AccountSeqSimpleInfo,
  AccountSeqDetailInfo,
  NoInvoicingItem,
  NoInvoicingSearchParams,
} from "../types/invoice";
import api from "./api";

export const getInvoiceList = async (
  params: InvoiceSearchParams
): Promise<ApiListResponse<InvoiceInfoItem[]>> => {
  const response = await api.get("/invoice-info", { params });
  return response.data;
};

export const createInvoice = async (data: any): Promise<any> => {
  const response = await api.post("/invoice-info", data);
  return response.data;
};

export const updateInvoice = async (
  id: number,
  data: any
): Promise<any> => {
  const response = await api.put(`/invoice-info/${id}`, data);
  return response.data;
};

export const getInvoiceDetail = async (
  id: number
): Promise<ApiResponse<InvoiceInfoItem>> => {
  const response = await api.get<ApiResponse<InvoiceInfoItem>>(
    `/invoice-info/${id}`
  );
  return response.data;
};

export interface InvoiceSimpleInfo {
  id: number;
  customer_invoice_name: string;
  customer_invoice_type: string;
}

// 获取发票信息简单列表
export const getInvocieInfoSimpleList = async (
  account_seq: string
): Promise<ApiResponse<InvoiceSimpleInfo[]>> => {
  const response = await api.get(`/invoice-info/simple-list`, {
    params: { account_seq },
  });
  return response.data;
};

// 获取分账序号简单列表
export const getAccountSeqSimpleList = async (
  customer_num?: string
): Promise<ApiResponse<AccountSeqSimpleInfo[]>> => {
  const params = customer_num ? { customer_num } : {};
  const response = await api.get("/account-seq/simple-list", { params });
  return response.data;
};

// 获取分账序号详细信息
export const getAccountSeqDetail = async (
  account_seq: string
): Promise<ApiResponse<AccountSeqDetailInfo>> => {
  const response = await api.get(`/account-seq/${account_seq}`);
  return response.data;
};

// 获取后付费发票列表
export const getPostPaidInvoiceList = async (
  params: PostPaidInvoiceSearchParams
): Promise<ApiListResponse<PostPaidInvoiceItem[]>> => {
  const response = await api.get("/post-paid-invoice", { params });
  return response.data;
};

// 批量预开票请求数据接口
export interface BatchPreInvoiceRequest {
  customer_num: string;
  account_seq: string;
  start_charge_month: number;
  end_charge_month: number;
}

// 批量预开票API
export const batchPreInvoice = async (
  data: BatchPreInvoiceRequest
): Promise<ApiResponse<any>> => {
  const response = await api.post("/post-paid-invoice/batch-issuance", data);
  return response.data;
};

// 预开票请求数据接口
export interface PreInvoiceRequest {
  charge_detail_ids: number[];
  exchange_rate: string;
}

// 预开票API
export const preInvoice = async (
  data: PreInvoiceRequest
): Promise<ApiResponse<any>> => {
  const response = await api.post("/post-paid-invoice/issuance", data);
  return response.data;
};

// 开票记录相关接口和类型
export interface InvoiceRecordItem {
  id: number;
  task_id: string;
  invoice_type: number; // 1-预开票, 2-批量预开票
  request_info: {
    account_seq?: string;
    customer_num?: string;
    end_charge_month?: number;
    start_charge_month?: number;
    exchange_rate?: string;
    charge_detail_ids?: number[];
  };
  state: number; // 1-开票中, 2-开票完成, 3-开票失败
  reason: string | null;
  create_user: string;
  updated_at: string;
}

export interface InvoiceRecordParams {
  page?: number;
  pageSize?: number;
}

// 获取开票记录列表
export const getInvoiceRecords = async (
  params: InvoiceRecordParams
): Promise<ApiListResponse<InvoiceRecordItem[]>> => {
  const response = await api.get("/invoice-record", { params });
  return response.data;
};

// 获取预付费发票列表
export const getPrePaidInvoiceList = async (
  params: PrePaidInvoiceSearchParams
): Promise<ApiListResponse<PrePaidInvoiceItem[]>> => {
  const response = await api.get("/pre-paid-invoice", { params });
  return response.data;
};

// 提交预付费开票
export const submitPrePaidInvoice = async (
  data: PrePaidInvoiceApiRequest
): Promise<ApiResponse<any>> => {
  const response = await api.post("/pre-paid-invoice/issuance", data);
  return response.data;
};

// 获取不开票列表
export const getNoInvoicingList = async (
  params: NoInvoicingSearchParams
): Promise<ApiListResponse<NoInvoicingItem[]>> => {
  const response = await api.get("/no-invoicing", { params });
  return response.data;
};

// 预开票相关接口

// 预开票列表项
export interface PreInvoicingItem {
  id: number;
  invoice_no: string;
  amount: number;
  account_seq: string;
  invoice_type: string;
  tax_rate: number;
  tax_amount: number;
  currency_type: string;
  invoice_currency_type: string;
  state: string;
  exchange_rate: number;
  signing_entity: string;
  invoice_file_id: number | null;
  remark: string;
  create_user: string;
  created_at: string;
  customer_name: string;
  customer_invoice_name: string;
}

// 预开票搜索参数
export interface PreInvoicingSearchParams {
  account_seq?: string;
  create_user?: string;
  customer_num?: string;
  page?: number;
  pageSize?: number;
}

// 预开票编辑数据
export interface PreInvoicingEditData {
  tax_rate: number;
  invoice_currency_type: string;
  exchange_rate: string;
  remark: string;
}

// 预开票批量操作数据
export interface PreInvoicingBatchAction {
  invoice_ids: number[];
  action: 'issuance' | 'pause' | 'cancel';
}

// 获取预开票列表
export const getPreInvoicingList = async (
  params: PreInvoicingSearchParams
): Promise<ApiListResponse<PreInvoicingItem[]>> => {
  const response = await api.get("/pre-invoicing", { params });
  return response.data;
};

// 更新预开票信息
export const updatePreInvoicing = async (
  id: number,
  data: PreInvoicingEditData
): Promise<ApiResponse<any>> => {
  const response = await api.put(`/pre-invoicing/${id}`, data);
  return response.data;
};

// 预开票批量操作
export const preInvoicingBatchAction = async (
  data: PreInvoicingBatchAction
): Promise<ApiResponse<any>> => {
  const response = await api.post("/pre-invoicing/issuance", data);
  return response.data;
};
